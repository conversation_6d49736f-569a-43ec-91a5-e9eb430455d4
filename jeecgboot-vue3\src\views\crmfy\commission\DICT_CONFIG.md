# 数据字典配置说明

## supplier 字典配置

佣金对账模块使用 `supplier` 字典来管理保险公司信息。请在系统管理 -> 字典管理中配置以下字典项：

### 字典基本信息
- **字典名称**: 保险公司
- **字典编码**: supplier
- **描述**: 保险公司列表

### 字典项配置示例

| 字典值 | 字典文本 | 排序 | 状态 | 备注 |
|--------|----------|------|------|------|
| AIA | 友邦保险 | 1 | 启用 | 友邦保险有限公司 |
| PRUDENTIAL | 保诚保险 | 2 | 启用 | 英国保诚保险有限公司 |
| MANULIFE | 宏利保险 | 3 | 启用 | 宏利人寿保险(国际)有限公司 |
| ZURICH | 苏黎世保险 | 4 | 启用 | 苏黎世保险有限公司 |
| GREAT_EASTERN | 大东方保险 | 5 | 启用 | 大东方人寿保险有限公司 |
| HSBC_LIFE | 汇丰人寿 | 6 | 启用 | 汇丰人寿保险有限公司 |
| FTLife | 富通保险 | 7 | 启用 | 富通保险有限公司 |
| CHINA_LIFE | 中国人寿 | 8 | 启用 | 中国人寿保险(海外)股份有限公司 |

### SQL 配置方式

如果需要通过 SQL 直接插入字典数据，可以使用以下语句：

```sql
-- 插入字典类型
INSERT INTO sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, type) 
VALUES ('supplier_dict_id', '保险公司', 'supplier', '保险公司列表', 0, 'admin', NOW(), 'admin', NOW(), 0);

-- 插入字典项
INSERT INTO sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time) VALUES
('supplier_item_1', 'supplier_dict_id', '友邦保险', 'AIA', '友邦保险有限公司', 1, 1, 'admin', NOW(), 'admin', NOW()),
('supplier_item_2', 'supplier_dict_id', '保诚保险', 'PRUDENTIAL', '英国保诚保险有限公司', 2, 1, 'admin', NOW(), 'admin', NOW()),
('supplier_item_3', 'supplier_dict_id', '宏利保险', 'MANULIFE', '宏利人寿保险(国际)有限公司', 3, 1, 'admin', NOW(), 'admin', NOW()),
('supplier_item_4', 'supplier_dict_id', '苏黎世保险', 'ZURICH', '苏黎世保险有限公司', 4, 1, 'admin', NOW(), 'admin', NOW()),
('supplier_item_5', 'supplier_dict_id', '大东方保险', 'GREAT_EASTERN', '大东方人寿保险有限公司', 5, 1, 'admin', NOW(), 'admin', NOW()),
('supplier_item_6', 'supplier_dict_id', '汇丰人寿', 'HSBC_LIFE', '汇丰人寿保险有限公司', 6, 1, 'admin', NOW(), 'admin', NOW()),
('supplier_item_7', 'supplier_dict_id', '富通保险', 'FTLife', '富通保险有限公司', 7, 1, 'admin', NOW(), 'admin', NOW()),
('supplier_item_8', 'supplier_dict_id', '中国人寿', 'CHINA_LIFE', '中国人寿保险(海外)股份有限公司', 8, 1, 'admin', NOW(), 'admin', NOW());
```

### 使用说明

1. **字典值**: 用作系统内部标识，建议使用英文大写字母和下划线
2. **字典文本**: 用于前端显示，使用中文名称
3. **排序**: 控制下拉框中选项的显示顺序
4. **状态**: 只有启用状态的字典项才会在前端显示

### 验证配置

配置完成后，可以通过以下方式验证：

1. 访问测试页面：`/crmfy/commission/test`
2. 点击"测试加载supplier字典"按钮
3. 查看返回的字典数据是否正确

### 注意事项

1. 字典编码 `supplier` 不能修改，系统代码中硬编码了这个值
2. 新增保险公司时，只需要在字典管理中添加新的字典项即可
3. 字典项的值会保存到数据库中，修改字典值可能影响历史数据
4. 建议在测试环境先验证字典配置，确认无误后再在生产环境配置
