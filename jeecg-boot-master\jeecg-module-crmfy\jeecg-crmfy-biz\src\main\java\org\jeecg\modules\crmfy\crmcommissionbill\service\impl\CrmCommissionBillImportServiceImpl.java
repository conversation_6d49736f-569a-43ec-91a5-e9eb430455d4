package org.jeecg.modules.crmfy.crmcommissionbill.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
// import org.apache.pdfbox.text.PDFTextStripper; // No longer needed with enhanced parser
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.ImportExcelUtil;
import org.jeecg.modules.crmfy.crmcommissionbill.config.InsuranceCompanyConfig;
import org.jeecg.modules.crmfy.crmcommissionbill.entity.CrmCommissionBill;
import org.jeecg.modules.crmfy.crmcommissionbill.service.ICrmCommissionBillImportService;
import org.jeecg.modules.crmfy.crmcommissionbill.service.ICrmCommissionBillService;
import org.jeecg.modules.crmfy.crmcommissionbill.utils.EnhancedPdfParser;
import org.jeecg.modules.crmfy.crmpolicies.entity.CrmPolicies;
import org.jeecg.modules.crmfy.crmpolicies.service.ICrmPoliciesService;
import org.jeecg.modules.jsjx.utils.MyDateUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.jeecg.common.util.RedisUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;

/**
 * Service implementation for importing commission bills from different formats
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Slf4j
@Service
public class CrmCommissionBillImportServiceImpl implements ICrmCommissionBillImportService {

    @Autowired
    private ICrmCommissionBillService crmCommissionBillService;

    @Autowired
    private InsuranceCompanyConfig insuranceCompanyConfig;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ICrmPoliciesService crmpoliciesService;

    // Company descriptions
    // Company descriptions
    private static final Map<String, String> COMPANY_DESCRIPTIONS = new HashMap<>();


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> importFromExcel(MultipartFile file, String companyCode) {
        try {

            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            // 设置当前处理的文件，以便重新打开输入流
            setCurrentFile(file);

            try {
                List<CrmCommissionBill> billList = parseExcel(file.getInputStream(), companyCode);

                // Validation and error handling
                List<String> errorMessages = new ArrayList<>();
                int successCount = 0;
                int updateCount = 0;

                Date curDate = new Date();

                for (int i = 0; i < billList.size(); i++) {
                    CrmCommissionBill bill = billList.get(i);
                    try {
                        validateBill(bill);
                        // Set common fields
                        setCommonFields(bill, companyCode);

                        // 检查该保单号是否已存在
                        QueryWrapper<CrmCommissionBill> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("policy_no", bill.getPolicyNo());
                        queryWrapper.eq("plan_name", bill.getPlanName());
                        // queryWrapper.eq("company_code", companyCode);
                        queryWrapper.eq("del_flag", 0); 
                        queryWrapper.orderByDesc("year_num") // 按期数字段降序排列
                        .last("LIMIT 1");           // 限制查询结果仅返回一条

                        CrmCommissionBill existingBill = crmCommissionBillService.getOne(queryWrapper);
                        //查询对应保单信息，获取保单生效日期
                        CrmPolicies crmPolicies = crmpoliciesService.getOne(new QueryWrapper<CrmPolicies>().eq("policy_no", bill.getPolicyNo()).eq("iz_active", 1));
                        if(crmPolicies==null) {
                        	//throw new JeecgBootException("保单号"+bill.getPolicyNo()+"不存在！");
                            errorMessages.add("第" + (i + 1) + "行: " + "保单号"+bill.getPolicyNo()+"不存在！");
                            continue;
                        }                    

                        if (existingBill != null) {
                            // 如果已存在
                            Date billDate = MyDateUtils.getExpectYear(crmPolicies.getCoolingOffDate(),existingBill.getYearNum());
                            if(curDate.compareTo(billDate)>0) {//交下一期保费
                            	bill.setYearNum(existingBill.getYearNum()+1);
                                crmCommissionBillService.save(bill);
                                successCount++;
                            }else{//更新已存在的数据
                                bill.setYearNum(existingBill.getYearNum());
                                bill.setId(existingBill.getId()); // 设置ID以便更新
                                //bill.setCreateBy(existingBill.getCreateBy()); // 保留原创建人
                                // bill.setCreateTime(existingBill.getCreateTime()); // 保留原创建时间
                                bill.setUpdateBy(loginUser.getUsername());
                                bill.setUpdateTime(curDate);
                                crmCommissionBillService.updateById(bill);
                                updateCount++;
                            }

                            
                           
                        } else {
                            // 如果不存在，则新增记录
                            bill.setYearNum(1);//首年佣金
                            crmCommissionBillService.save(bill);
                            successCount++;
                        }
                    } catch (Exception e) {
                        errorMessages.add("第" + (i + 1) + "行: " + e.getMessage());
                    }
                }

                // 在错误消息中添加成功和更新的统计信息
                if (errorMessages.isEmpty()) {
                    // 如果没有错误，直接返回成功结果
                    return Result.ok("导入成功！共" + (successCount + updateCount) + "条，其中新增" + successCount + "条，更新" + updateCount + "条。");
                } else {
                    // 如果有错误，在错误消息列表开头添加成功和更新的统计信息
                    errorMessages.add(0, "导入统计：共" + (successCount + updateCount) + "条成功，其中新增" + successCount + "条，更新" + updateCount + "条。失败" + errorMessages.size() + "条。");
                    return ImportExcelUtil.imporReturnRes(errorMessages.size() - 1, successCount + updateCount, errorMessages);
                }
            } finally {
                // 清除当前处理的文件
                clearCurrentFile();
            }
        } catch (Exception e) {
            log.error("Import Excel error", e);
            return Result.error("导入Excel失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> importFromPdf(MultipartFile file, String companyCode) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        try {
            // 设置当前处理的文件，以便重新打开输入流
            setCurrentFile(file);

            try {
                List<CrmCommissionBill> billList = parsePdf(file.getInputStream(), companyCode);

                // Validation and error handling
                List<String> errorMessages = new ArrayList<>();
                int successCount = 0;
                int updateCount = 0;
                Date curDate = new Date();

                for (int i = 0; i < billList.size(); i++) {
                    CrmCommissionBill bill = billList.get(i);
                    try {
                        validateBill(bill);
                        // Set common fields
                        setCommonFields(bill, companyCode);

                       // 检查该保单号是否已存在
                        QueryWrapper<CrmCommissionBill> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("policy_no", bill.getPolicyNo());
                        queryWrapper.eq("plan_name", bill.getPlanName());
                        // queryWrapper.eq("company_code", companyCode);
                        queryWrapper.eq("del_flag", 0); 
                        queryWrapper.orderByDesc("year_num") // 按期数字段降序排列
                        .last("LIMIT 1");           // 限制查询结果仅返回一条

                        CrmCommissionBill existingBill = crmCommissionBillService.getOne(queryWrapper);
                        //查询对应保单信息，获取保单生效日期
                        CrmPolicies crmPolicies = crmpoliciesService.getOne(new QueryWrapper<CrmPolicies>().eq("policy_no", bill.getPolicyNo()).eq("iz_active", 1));
                        if(crmPolicies==null) {
                        	//throw new JeecgBootException("保单号"+bill.getPolicyNo()+"不存在！");
                            errorMessages.add("第" + (i + 1) + "行: " + "保单号"+bill.getPolicyNo()+"不存在！");
                            continue;
                        }                    

                        if (existingBill != null) {
                            // 如果已存在
                            Date billDate = MyDateUtils.getExpectYear(crmPolicies.getCoolingOffDate(),existingBill.getYearNum());
                            if(curDate.compareTo(billDate)>0) {//交下一期保费
                            	bill.setYearNum(existingBill.getYearNum()+1);
                                crmCommissionBillService.save(bill);
                                successCount++;
                            }else{//更新已存在的数据
                                bill.setYearNum(existingBill.getYearNum());
                                bill.setId(existingBill.getId()); // 设置ID以便更新
                                //bill.setCreateBy(existingBill.getCreateBy()); // 保留原创建人
                                // bill.setCreateTime(existingBill.getCreateTime()); // 保留原创建时间
                                bill.setUpdateBy(loginUser.getUsername());
                                bill.setUpdateTime(curDate);
                                crmCommissionBillService.updateById(bill);
                                updateCount++;
                            }

                            
                           
                        } else {
                            // 如果不存在，则新增记录
                            bill.setYearNum(1);//首年佣金
                            crmCommissionBillService.save(bill);
                            successCount++;
                        }
                    } catch (Exception e) {
                        errorMessages.add("第" + (i + 1) + "行: " + e.getMessage());
                    }
                }

                // 在错误消息中添加成功和更新的统计信息
                if (errorMessages.isEmpty()) {
                    // 如果没有错误，直接返回成功结果
                    return Result.ok("导入成功！共" + (successCount + updateCount) + "条，其中新增" + successCount + "条，更新" + updateCount + "条。");
                } else {
                    // 如果有错误，在错误消息列表开头添加成功和更新的统计信息
                    errorMessages.add( "导入统计：共" + (successCount + updateCount) + "条成功，其中新增" + successCount + "条，更新" + updateCount + "条。失败" + errorMessages.size() + "条。");
                    return ImportExcelUtil.imporReturnRes(errorMessages.size() - 1, successCount + updateCount, errorMessages);
                }
            } finally {
                // 清除当前处理的文件
                clearCurrentFile();
            }
        } catch (Exception e) {
            log.error("Import PDF error", e);
            return Result.error("导入PDF失败: " + e.getMessage());
        }
    }

    @Override
    public List<CrmCommissionBill> parseExcel(InputStream inputStream, String companyCode) {
        try {
            log.info("开始解析Excel文件，保险公司代码: {}", companyCode);

            // Get field mappings for the company
            Map<String, String> fieldMappings = new HashMap<>();
            if (fieldMappings.isEmpty()) {
                // 如果在配置中找不到映射，尝试从Redis获取
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, String> fieldMappingsRedis = (Map<String, String>) redisUtil.hget("dictList:", "commission_bill_field");
                    if (fieldMappingsRedis != null && !fieldMappingsRedis.isEmpty()) {
                        // 将Redis中的映射转换为适合Excel导入的格式
                        Map<String, String> excelFieldMappings = new HashMap<>();
                        for (Map.Entry<String, String> entry : fieldMappingsRedis.entrySet()) {
                            String key = entry.getKey();
                            String[] values = entry.getValue().split(",");
                            for (String value : values) {
                                excelFieldMappings.put(value, key);
                            }
                        }
                        fieldMappings = excelFieldMappings;
                    }
                } catch (Exception e) {
                    log.warn("从Redis获取字段映射失败", e);
                }

                if (fieldMappings.isEmpty()) {
                    throw new RuntimeException("不支持的保险公司代码: " + companyCode);
                }
            }

            // 动态检测Excel结构
            ImportParams params = new ImportParams();

            // 尝试自动检测标题行和表头行
            int[] headerInfo = detectHeaderRows(inputStream, fieldMappings);
            int titleRows = headerInfo[0];
            int headRows = headerInfo[1];

            log.info("检测到标题行: {}, 表头行: {}", titleRows, headRows);

            // 设置导入参数
            params.setTitleRows(titleRows);
            params.setHeadRows(headRows);

            // 重新打开输入流，因为检测过程中已经读取了部分数据
            inputStream.close();
            InputStream newInputStream = reopenInputStream(companyCode);

            // 导入Excel为Map列表
            List<Map<String, Object>> listMap = ExcelImportUtil.importExcel(newInputStream, Map.class, params);
            log.info("从Excel导入了 {} 行数据", listMap.size());

            // 转换为CrmCommissionBill对象列表
            List<CrmCommissionBill> billList = new ArrayList<>();
            int rowNum = 0;
            boolean breakFlag = false;
            for (Map<String, Object> map : listMap) {
            	if(breakFlag) {
            		break;
            	}
                rowNum++;
                try {
                    CrmCommissionBill bill = new CrmCommissionBill();
                    boolean hasValidData = false;
                    // 基于公司配置映射字段
                    for (Map.Entry<String, Object> entry : map.entrySet()) {
                        if(entry.getKey()==null) {
                        	continue ;
                        }
                        String sourceField = entry.getKey().replaceAll("[\\s\\r\\n]", "");
                        Object value = entry.getValue();

                        String targetField = fieldMappings.get(sourceField);

                        if(targetField != null&&targetField.equals("policyNo")&&(value == null ||StringUtils.isBlank(value.toString())||(value != null&&value.toString().contains("Total")))) {
                        	breakFlag = true;
                        	break;
                        }
                        if (value != null && StringUtils.isNotBlank(value.toString())) {
                            // 尝试直接匹配

                            // 如果直接匹配失败，尝试模糊匹配
//                            if (targetField == null) {
//                                targetField = findBestMatchField(sourceField, fieldMappings);
//                            }


                            if (targetField != null) {
                                setFieldValue(bill, targetField, value);
                                hasValidData = true;
                            } else {
                                log.debug("第{}行: 未找到字段 '{}' 的映射", rowNum, sourceField);
                            }
                        }
                    }

                    if(breakFlag) {
                		break;
                	}
                    // 设置公司代码
                    bill.setCompanyCode(companyCode);

                    // 只添加包含有效数据的账单
                    if (hasValidData && bill.getPolicyNo() != null) {
                    	 if(bill.getCommissionAmount()==null) {
                         	if(bill.getAfyc()!=null&&bill.getAfyc().compareTo(BigDecimal.ZERO)>0) {
                         		bill.setCommissionAmount(bill.getAfyc()) ;
                             }else if(bill.getFyc()!=null&&bill.getFyc().compareTo(BigDecimal.ZERO)>0){
                             	bill.setCommissionAmount(bill.getFyc());
                             }else {
                             	bill.setCommissionAmount(bill.getRc());
                             }
                         }

                         if(bill.getPremiumAmount()==null) {
                         	if(bill.getFyp()!=null&&bill.getFyp().compareTo(BigDecimal.ZERO)>0){
                         		bill.setPremiumAmount(bill.getFyp());
                             }else {
                             	bill.setPremiumAmount(bill.getRp());
                             }

                         }
                        if(StringUtils.isNotBlank(bill.getPolicyNo())) {
                        	billList.add(bill);
                        }

                    } else {
                        log.warn("第{}行: 跳过无效数据行", rowNum);
                    }
                } catch (Exception e) {
                    log.warn("处理第{}行时出错: {}", rowNum, e.getMessage());
                }
            }

            log.info("成功解析 {} 条佣金账单记录", billList.size());
            return billList;
        } catch (Exception e) {
            log.error("解析Excel出错", e);
            throw new RuntimeException("解析Excel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检测Excel文件中的标题行和表头行
     *
     * @param inputStream Excel输入流
     * @param fieldMappings 字段映射
     * @return 包含[标题行数, 表头行数]的数组
     */
    private int[] detectHeaderRows(InputStream inputStream, Map<String, String> fieldMappings) throws Exception {
        // 默认值
        int titleRows = 0;
        int headRows = 1;

        // 获取文件名，用于特殊处理
        MultipartFile file = currentFile.get();
        String fileName = file != null ? file.getOriginalFilename() : "";

        // 特殊处理RCM0087文件
        if (fileName != null && fileName.contains("RCM0087")) {
            log.info("检测到RCM0087格式文件，使用特殊处理: 标题行=6, 表头行=1");
            return new int[] {6, 1}; // 标题行=6，表头行=1（根据实际文件结构设置）
        }

        try {
            // 创建工作簿
            org.apache.poi.ss.usermodel.Workbook workbook = org.apache.poi.ss.usermodel.WorkbookFactory.create(inputStream);
            org.apache.poi.ss.usermodel.Sheet sheet = workbook.getSheetAt(0);

            // 最大检查行数
            int maxRowsToCheck = Math.min(10, sheet.getLastRowNum() + 1);

            // 存储每行匹配的字段数
            int[] matchesPerRow = new int[maxRowsToCheck];

            // 检查每一行
            for (int i = 0; i < maxRowsToCheck; i++) {
                org.apache.poi.ss.usermodel.Row row = sheet.getRow(i);
                if (row == null) continue;

                // 检查这一行的每个单元格
                int matches = 0;
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    org.apache.poi.ss.usermodel.Cell cell = row.getCell(j);
                    if (cell == null) continue;

                    String cellValue = "";
                    try {
                        // 尝试获取单元格的字符串值
                        cellValue = cell.getStringCellValue();
                    } catch (Exception e) {
                        // 如果不是字符串，尝试其他类型
                        try {
                            cellValue = String.valueOf(cell.getNumericCellValue());
                        } catch (Exception e2) {
                            // 忽略其他类型错误
                        }
                    }

                    // 检查这个单元格的值是否在字段映射中
                    if (StringUtils.isNotBlank(cellValue) &&
                        (fieldMappings.containsKey(cellValue) || findBestMatchField(cellValue, fieldMappings) != null)) {
                        matches++;
                    }
                }

                matchesPerRow[i] = matches;
                log.debug("第{}行匹配字段数: {}", i+1, matches);
            }

            // 找出匹配字段最多的行作为表头行
            int maxMatches = 0;
            int headerRowIndex = 0;

            for (int i = 0; i < matchesPerRow.length; i++) {
                if (matchesPerRow[i] > maxMatches) {
                    maxMatches = matchesPerRow[i];
                    headerRowIndex = i;
                }
            }

            // 如果找到了表头行
            if (maxMatches > 0) {
                // 表头行之前的所有行都视为标题行
                titleRows = headerRowIndex;
                headRows = 1;  // 表头行本身
                log.info("检测到表头行在第{}行，匹配字段数: {}", headerRowIndex + 1, maxMatches);
            } else {
                // 如果没有找到明确的表头行，使用默认值
                log.warn("未检测到明确的表头行，使用默认值: 标题行=0, 表头行=1");
            }

            workbook.close();
        } catch (Exception e) {
            log.warn("检测表头行时出错，使用默认值", e);
        }

        return new int[] {titleRows, headRows};
    }

    // 存储当前处理的MultipartFile，用于重新打开输入流
    private static ThreadLocal<MultipartFile> currentFile = new ThreadLocal<>();

    /**
     * 设置当前处理的文件
     *
     * @param file 当前处理的MultipartFile
     */
    public void setCurrentFile(MultipartFile file) {
        currentFile.set(file);
    }

    /**
     * 清除当前处理的文件
     */
    public void clearCurrentFile() {
        currentFile.remove();
    }

    /**
     * 重新打开输入流
     *
     * @param companyCode 公司代码
     * @return 新的输入流
     */
    private InputStream reopenInputStream(String companyCode) throws IOException {
        MultipartFile file = currentFile.get();
        if (file != null) {
            return file.getInputStream();
        } else {
            // 如果没有设置当前文件，则尝试从样例文件中读取（仅用于测试）
            log.warn("没有设置当前处理的文件，尝试从样例文件中读取（仅用于测试）");
            String filePath = "E:\\workspace\\jsjxNewSpace\\jeecg-boot-master\\jeecg-module-crmfy\\jeecg-crmfy-biz\\src\\main\\java\\org\\jeecg\\modules\\crmfy\\crmcommissionbill\\fileimport\\boc.xls";
            return new java.io.FileInputStream(filePath);
        }
    }

    /**
     * 查找最佳匹配字段
     *
     * @param sourceField 源字段名
     * @param fieldMappings 字段映射
     * @return 最佳匹配的目标字段，如果没有找到则返回null
     */
    private String findBestMatchField(String sourceField, Map<String, String> fieldMappings) {
        if (sourceField == null || sourceField.trim().isEmpty()) {
            return null;
        }

        // 标准化源字段名（去除空格、转小写）
        String normalizedSource = sourceField.trim().toLowerCase();

        // 1. 尝试直接匹配（不区分大小写）
        for (Map.Entry<String, String> entry : fieldMappings.entrySet()) {
            if (entry.getKey().trim().toLowerCase().equals(normalizedSource)) {
                return entry.getValue();
            }
        }

        // 2. 尝试包含匹配
//        for (Map.Entry<String, String> entry : fieldMappings.entrySet()) {
//            String key = entry.getKey().trim().toLowerCase();
//            if (key.contains(normalizedSource) || normalizedSource.contains(key)) {
//                return entry.getValue();
//            }
//        }

        // 3. 尝试关键词匹配
        Map<String, String> keywordMap = new HashMap<>();
        keywordMap.put("policy", "policyNo");
        keywordMap.put("保单", "policyNo");
        keywordMap.put("单号", "policyNo");
        keywordMap.put("号码", "policyNo");

        keywordMap.put("effect", "inforceDate");
        keywordMap.put("生效", "inforceDate");
        keywordMap.put("inforce", "inforceDate");

        keywordMap.put("trans", "transactionDate");
        keywordMap.put("处理", "transactionDate");
        keywordMap.put("日期", "transactionDate");

        keywordMap.put("plan", "planName");
        keywordMap.put("产品", "planName");
        keywordMap.put("名称", "planName");

        keywordMap.put("code", "planCode");
        keywordMap.put("代码", "planCode");

        keywordMap.put("curr", "paymentCurrency");
        keywordMap.put("币种", "paymentCurrency");

        keywordMap.put("prem", "premiumAmount");
        keywordMap.put("保费", "premiumAmount");

        keywordMap.put("rate", "commissionRate");
        keywordMap.put("佣金率", "commissionRate");

        keywordMap.put("comm", "commissionAmount");
        keywordMap.put("佣金", "commissionAmount");

        keywordMap.put("fyp", "fyp");
        keywordMap.put("首年保费", "fyp");

        keywordMap.put("rp", "rp");
        keywordMap.put("续期保费", "rp");

        keywordMap.put("rc", "rc");
        keywordMap.put("续期佣金", "rc");

        keywordMap.put("afyc", "afyc");
        keywordMap.put("标准佣金", "afyc");

        keywordMap.put("fyc", "fyc");
        keywordMap.put("首年佣金", "fyc");

        keywordMap.put("exchange", "rcExchangeRate");
        keywordMap.put("汇率", "rcExchangeRate");

        keywordMap.put("agent", "saleName");
        keywordMap.put("销售", "saleName");

        for (Map.Entry<String, String> entry : keywordMap.entrySet()) {
            if (normalizedSource.contains(entry.getKey())) {
                return entry.getValue();
            }
        }

        // 没有找到匹配
        return null;
    }

    @Override
    public List<CrmCommissionBill> parsePdf(InputStream inputStream, String companyCode) {
        try {
            // Get field mappings for the company from redis

            @SuppressWarnings("unchecked")
            Map<String, String> fieldMappingsRedis  = (Map<String, String>)redisUtil.hget("dictList:", "commission_bill_field");
            //遍历fieldMappings
            Map<String, String> fieldMappings  = new HashMap<>();
            for (Map.Entry<String, String> entry : fieldMappingsRedis.entrySet()) {
                String key = entry.getKey();
                String[] values = entry.getValue().split(",");
                //遍历values
                for (String value : values) {
                    fieldMappings.put(value, key);
                }

            }
            //Map<String, String> fieldMappings = insuranceCompanyConfig.getFieldMappings("FWD");
            if (fieldMappings.isEmpty()) {
                throw new RuntimeException("不支持的保险公司代码: " + companyCode);
            }

            // Load PDF document
            PDDocument document = PDDocument.load(inputStream);

            try {
                // Use the enhanced PDF parser that handles text positions
                log.info("Using enhanced PDF parser for company code: {}", companyCode);
               EnhancedPdfParser enhancedParser = new EnhancedPdfParser();

                // Process the PDF document
                List<CrmCommissionBill> billList = enhancedParser.processPdf(document, fieldMappings, companyCode,redisUtil);

                log.info("Enhanced parser extracted {} commission bills from PDF", billList.size());
                return billList;
            } finally {
                // Ensure document is closed
                document.close();
            }
        } catch (IOException e) {
            log.error("Parse PDF error", e);
            throw new RuntimeException("解析PDF失败: " + e.getMessage(), e);
        }
    }



    /**
     * Note: The old header line combining method has been replaced by more robust
     * position-based text extraction in the EnhancedPdfParser class.
     */

    @Override
    public Map<String, String> getSupportedCompanies() {
        Map<String, String> result = new LinkedHashMap<>();
        for (String companyCode : insuranceCompanyConfig.getSupportedCompanies()) {
            result.put(companyCode, COMPANY_DESCRIPTIONS.getOrDefault(companyCode, companyCode));
        }
        return result;
    }

    /**
     * Set common fields for a commission bill
     *
     * @param bill The commission bill
     * @param companyCode The company code
     */
    private void setCommonFields(CrmCommissionBill bill, String companyCode) {
        // Get current user
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        // Set common fields
        bill.setCompanyCode(companyCode);
        bill.setSysOrgCode(loginUser.getOrgCode());
        bill.setCreateBy(loginUser.getUsername());
        bill.setCreateTime(new Date());
        bill.setUpdateTime(new Date());
        bill.setDelFlag(0);
        if(StringUtils.isNotBlank(bill.getPaymentCurrency())&&bill.getPaymentCurrency().equals("HKD")){
            bill.setPaymentCurrency("HK");
        }
    }

    /**
     * Validate a commission bill
     *
     * @param bill The commission bill
     * @throws Exception If validation fails
     */
    private void validateBill(CrmCommissionBill bill) throws Exception {
        if (StringUtils.isBlank(bill.getPolicyNo())) {
            throw new Exception("保单号不能为空");
        }

//        if (bill.getInforceDate() == null) {
//            throw new Exception("保单生效日期不能为空");
//        }

        if (bill.getCommissionAmount() == null || bill.getCommissionAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new JeecgBootException("佣金金额必须大于0");
        }
    }

    /**
     * Set a field value on a commission bill
     *
     * @param bill The commission bill
     * @param fieldName The field name
     * @param value The field value
     */
    private void setFieldValue(CrmCommissionBill bill, String fieldName, Object value) {
        try {
            switch (fieldName) {
                case "policyNo":
                    bill.setPolicyNo(value.toString());
                    break;
                case "inforceDate":
                    bill.setInforceDate(parseDate(value));
                    break;
                case "transactionDate":
                    bill.setTransactionDate(parseDate(value));
                    break;
                case "planName":
                    bill.setPlanName(value.toString());
                    break;
                case "planCode":
                    bill.setPlanCode(value.toString());
                    break;
                case "paymentCurrency":
                    bill.setPaymentCurrency(value.toString());
                    break;
                case "premiumAmount":
                    bill.setPremiumAmount(parseDecimal(value));
                    break;
                case "commissionRate":
                    bill.setCommissionRate(parseDecimal(value));
                    break;
                case "commissionAmount":
                    bill.setCommissionAmount(parseDecimal(value));
                    break;
                case "fyp":
                    bill.setFyp(parseDecimal(value));
                    break;
                case "rp":
                    bill.setRp(parseDecimal(value));
                    break;
                case "rc":
                    bill.setRc(parseDecimal(value));
                    break;
                case "afyc":
                    bill.setAfyc(parseDecimal(value));
                    break;
                case "fyc":
                    bill.setFyc(parseDecimal(value));
                    break;
                case "rcExchangeRate":
                    bill.setRcExchangeRate(parseDecimal(value));
                    break;
                // case "saleName":
                //     bill.setSaleName(value.toString());
                //     break;
                default:
                    log.warn("Unknown field: {}", fieldName);
            }
        } catch (Exception e) {
            log.warn("Failed to set field value: {} = {}", fieldName, value, e);
        }
    }

    /**
     * Parse a date value with improved format detection
     *
     * @param value The date value
     * @return The parsed date
     */
    private Date parseDate(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Date) {
            return (Date) value;
        }

        String strValue = value.toString().trim();
        if (StringUtils.isBlank(strValue)) {
            return null;
        }

        try {
            // First, try to determine the most likely format based on the pattern
            String format = detectDateFormat(strValue);
            if (format != null) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(format);
                    sdf.setLenient(false); // Strict parsing to avoid invalid dates
                    return sdf.parse(strValue);
                } catch (ParseException e) {
                    // If detected format fails, continue with other formats
                }
            }

            // Try different date formats in order of likelihood
            String[] formats = {
                "dd/MM/yyyy", // European format (most common for dates like 19/05/2021)
                "MM/dd/yyyy", // US format
                "yyyy/MM/dd", // ISO-like format with slashes
                "dd-MM-yyyy", // European format with dashes
                "MM-dd-yyyy", // US format with dashes
                "yyyy-MM-dd", // ISO format
                "yyyy-MM-dd HH:mm:ss", // ISO format with time
                "yyyy/MM/dd HH:mm:ss"  // ISO-like format with slashes and time
            };

            for (String fmt : formats) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(fmt);
                    sdf.setLenient(false); // Strict parsing to avoid invalid dates
                    return sdf.parse(strValue);
                } catch (ParseException e) {
                    // Try next format
                }
            }

            // If all standard formats fail, try with different separators
            if (strValue.contains(".")) {
                String[] dotFormats = {"dd.MM.yyyy", "MM.dd.yyyy", "yyyy.MM.dd"};
                for (String fmt : dotFormats) {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat(fmt);
                        sdf.setLenient(false);
                        return sdf.parse(strValue);
                    } catch (ParseException e) {
                        // Try next format
                    }
                }
            }

            log.warn("Unable to parse date: {}", strValue);
            return null;
        } catch (Exception e) {
            log.warn("Failed to parse date: {}", strValue, e);
            return null;
        }
    }

    /**
     * Detect the most likely date format based on the pattern of the date string
     *
     * @param dateStr Date string
     * @return The most likely date format pattern, or null if undetermined
     */
    private String detectDateFormat(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }

        // Check for common separators
        char separator = '/';
        if (dateStr.contains("-")) {
            separator = '-';
        } else if (dateStr.contains(".")) {
            separator = '.';
        } else if (!dateStr.contains("/")) {
            return null; // No recognized separator
        }

        // Split the date string by the separator
        String[] parts = dateStr.split(String.valueOf(separator));
        if (parts.length != 3) {
            return null; // Not a standard date format
        }

        // Check the pattern based on the parts
        if (parts[0].length() == 4) {
            // If first part is 4 digits, likely yyyy-MM-dd
            return "yyyy" + separator + "MM" + separator + "dd";
        } else if (parts[2].length() == 4) {
            // If third part is 4 digits
            // Check if first part is likely day or month
            int firstPart = -1;
            try {
                firstPart = Integer.parseInt(parts[0]);
            } catch (NumberFormatException e) {
                return null;
            }

            if (firstPart > 12) {
                // If first part > 12, it must be a day (dd/MM/yyyy)
                return "dd" + separator + "MM" + separator + "yyyy";
            } else if (firstPart > 0) {
                // If first part is 1-12, it could be either day or month
                // For dates like 01/05/2021, prefer dd/MM/yyyy (European format)
                // as it's more common internationally
                return "dd" + separator + "MM" + separator + "yyyy";
            }
        }

        // Default to dd/MM/yyyy if we can't determine
        return "dd" + separator + "MM" + separator + "yyyy";
    }

    /**
     * Parse a decimal value
     *
     * @param value The decimal value
     * @return The parsed decimal
     */
    private BigDecimal parseDecimal(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }

        if (value instanceof Number) {
            return new BigDecimal(((Number) value).toString());
        }

        String strValue = value.toString().trim();
        if (StringUtils.isBlank(strValue)) {
            return null;
        }

        try {
            // Remove any non-numeric characters except decimal point and minus sign
            strValue = strValue.replaceAll("[^0-9\\.\\-]", "");
            return new BigDecimal(strValue);
        } catch (Exception e) {
            log.warn("Failed to parse decimal: {}", strValue, e);
            return null;
        }
    }

    /**
     * Note: The old PDF parsing methods have been replaced by the EnhancedPdfParser class
     * which provides better handling of PDF content with position-based extraction.
     */
}
