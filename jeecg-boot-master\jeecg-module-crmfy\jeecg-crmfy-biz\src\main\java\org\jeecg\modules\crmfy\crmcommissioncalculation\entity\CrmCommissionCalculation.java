package org.jeecg.modules.crmfy.crmcommissioncalculation.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 佣金计算表
 * @Author: jeecg-boot
 * @Date:   2025-04-11
 * @Version: V1.0
 */
@Data
@TableName("crm_commission_calculation")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_commission_calculation对象", description="佣金计算表")
public class CrmCommissionCalculation {
    
	/**计算ID*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "计算ID")
	private java.lang.Integer id;

	@ApiModelProperty(value = "保单ID")
	private java.lang.Integer policyId;
	/**保单号*/
	@Excel(name = "保单号", width = 15)
    @ApiModelProperty(value = "保单号")
	private java.lang.String policyNo;

	@Excel(name = "计划名称", width = 15)
    @ApiModelProperty(value = "计划名称")
	private java.lang.String planName;

    @Excel(name = "公司", width = 15)
    @ApiModelProperty(value = "公司编码")
	private java.lang.String companyCode;
	
	/**费率ID*/
	@Excel(name = "费率ID", width = 15)
    @ApiModelProperty(value = "费率ID")
	private java.lang.Integer rateId;
	/**第几年*/
	@Excel(name = "第几年", width = 15)
    @ApiModelProperty(value = "第几年")
	private java.lang.Integer yearNum;
	/**保费金额*/
	@Excel(name = "保费金额", width = 15)
    @ApiModelProperty(value = "保费金额")
	private java.math.BigDecimal premiumAmount;
	/**佣金率*/
	@Excel(name = "佣金率", width = 15)
    @ApiModelProperty(value = "佣金率")
	private java.math.BigDecimal commissionRate;
	
	@Excel(name = "合约手续费率", width = 15)
    @ApiModelProperty(value = "合约手续费率")
	private java.math.BigDecimal contractRate;
	
	/**佣金金额*/
	@Excel(name = "佣金金额", width = 15)
    @ApiModelProperty(value = "佣金金额")
	private java.math.BigDecimal commissionAmount;
	/**公司发放比例*/
	@Excel(name = "公司发放比例", width = 15)
    @ApiModelProperty(value = "公司发放比例")
	private java.math.BigDecimal companyPayoutRatio;
	/**公司手续费(台账)*/
	@Excel(name = "公司手续费(台账)", width = 15)
    @ApiModelProperty(value = "公司手续费(台账)")
	private java.math.BigDecimal companyFee;
	/**佣金结算日当天汇率*/
	@Excel(name = "佣金结算日当天汇率", width = 15)
    @ApiModelProperty(value = "佣金结算日当天汇率")
	private java.math.BigDecimal rcExchangeRate;
	/**缴费币种*/
	@Excel(name = "缴费币种", width = 15)
    @ApiModelProperty(value = "缴费币种")
	private java.lang.String paymentCurrency;
	/**计算日期*/
	@Excel(name = "计算日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "计算日期")
	private java.util.Date calculationDate;
	/**发放状态(0 待发放/1 已发放/2撤销)*/
	@Excel(name = "发放状态(0 待发放/1 已发放/2撤销)", width = 15)
    @ApiModelProperty(value = "发放状态(0 待发放/1 已发放/2撤销)")
	private java.lang.String payoutStatus;
	/**发放日期*/
	@Excel(name = "发放日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "发放日期")
	private java.util.Date payoutDate;
	/**部门*/
	@Excel(name = "部门", width = 15)
    @ApiModelProperty(value = "部门")
	private java.lang.String sysOrgCode;
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**创建时间*/
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private java.util.Date createTime;
	/**修改人*/
	@Excel(name = "修改人", width = 15)
    @ApiModelProperty(value = "修改人")
	private java.lang.String updateBy;
	/**修改时间*/
	@Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
	private java.util.Date updateTime;
	/**0表示未删除,1表示删除*/
	@Excel(name = "0表示未删除,1表示删除", width = 15)
    @ApiModelProperty(value = "0表示未删除,1表示删除")
	private java.lang.Integer delFlag;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
	private java.lang.Integer tenantId;
	/**销售员username*/
	@Excel(name = "销售员username", width = 15)
    @ApiModelProperty(value = "销售员username")
	private java.lang.String saleName;
}
