package org.jeecg.modules.crmfy.crmcommissionbill.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.crmfy.crmcommissionbill.entity.CrmCommissionBill;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Enhanced PDF parser for commission statements
 * Based on the approach used in CommissionStatementParser21
 */

@Slf4j
public class EnhancedPdfParser extends PDFTextStripper {

    private RedisUtil redisUtil;

    private List<List<TextPosition>> lines = new ArrayList<>();
    private List<TextPosition> currentLine = new ArrayList<>();
    private float lastY = -1;
    private float toleranceY = 4.0f; // Vertical tolerance for line detection

    // Document metadata
    private String documentDate;
    private String companyCode;
    private Map<String, String> fieldMappings;

    // Column information
    private List<ColumnInfo> columns = new ArrayList<>();

    public EnhancedPdfParser() throws IOException {
        super();
    }

    /**
     * Process a PDF document with the specified field mappings
     *
     * @param document The PDF document
     * @param fieldMappings The field mappings
     * @param companyCode The company code
     * @param redisUtil2
     * @return List of commission bills
     */
    public List<CrmCommissionBill> processPdf(PDDocument document, Map<String, String> fieldMappings, String companyCode, RedisUtil redisUtil) throws IOException {
        this.fieldMappings = fieldMappings;
        this.companyCode = companyCode;
        this.redisUtil = redisUtil;
        // Configure the parser
        setSortByPosition(true);
        setStartPage(1);
        setEndPage(document.getNumberOfPages());

        // Extract text with position information
        lines.clear();
        getText(document);

        // Process the document based on company format
        return processBrokerFormat();

/*        if ("FWD".equals(companyCode)) {
            return processBrokerFormat();
        } else if ("LIFE_ASIA".equals(companyCode)) {
            return processLifeAsiaFormat();
        } else {
            return processGenericFormat();
        }*/
    }

    @Override
    protected void writeString(String text, List<TextPosition> textPositions) throws IOException {
        // 收集每一行的文本位置信息
        for (TextPosition position : textPositions) {
            float y = position.getY();

            // 检测是否是新行
            if (lastY == -1 || Math.abs(y - lastY) > toleranceY) {
                if (!currentLine.isEmpty()) {
                    Collections.sort(currentLine, Comparator.comparing(TextPosition::getX));
                    lines.add(new ArrayList<>(currentLine));
                    currentLine.clear();
                }
                lastY = y;
            }

            currentLine.add(position);
        }

        super.writeString(text, textPositions);
    }

    @Override
    public void endDocument(PDDocument document) throws IOException {
        // 确保处理最后一行
        if (!currentLine.isEmpty()) {
            Collections.sort(currentLine, Comparator.comparing(TextPosition::getX));
            lines.add(new ArrayList<>(currentLine));
        }
        super.endDocument(document);
    }

    /**
     * Process Broker format PDF
     *
     * @return List of commission bills
     */
    public List<CrmCommissionBill> processBrokerFormat() {
        List<CrmCommissionBill> billList = new ArrayList<>();


        extractDocumentDate();

        // Find table header from redis
        @SuppressWarnings("unchecked")
        Map<String, String> headStartMap = (Map<String, String>) redisUtil.hget("dictList:","commission_bill_head_start");
        String headStr = headStartMap.get("headStr");

        // String headStr = "Policy,FYC,Plan" ;

        int headerIndex = findTableHeader(headStr.split(","));
        if (headerIndex == -1) {
            return billList;
        }

        // Find data rows
        int firstDataRowIndex = findFirstDataRow(headerIndex);
        int lastDataRowIndex = findLastDataRow(firstDataRowIndex);

        if (firstDataRowIndex == -1) {
            return billList;
        }

        // Identify columns
//        identifyColumns(headerIndex, firstDataRowIndex, lastDataRowIndex);
        float columnToleranceHead =(float) 4.5 ;//坐标容差值
        float columnToleranceData =(float) 3.0 ;//坐标容差值
        
        //获取坐标容错
        @SuppressWarnings("unchecked")
        Map<String, String> toleranceMap = (Map<String, String>) redisUtil.hget("dictList:","commission_bill_tolerance");

        String tolerances  = toleranceMap.get(companyCode);
        if(tolerances!=null) {
        	String[] toleranceArr =tolerances.toString().split(",");
        	columnToleranceHead = Float.parseFloat(toleranceArr[0]);
            columnToleranceData = Float.parseFloat(toleranceArr[1]);
            toleranceY = Float.parseFloat(toleranceArr[2]);
            log.info("坐标容错值:{}",tolerances);
        }
        

        columns = processMultilineHeader(headerIndex,lastDataRowIndex,firstDataRowIndex, columnToleranceHead,columnToleranceData) ;


        // Extract data rows
        for (int i = firstDataRowIndex; i <= lastDataRowIndex+1; i++) {
            CrmCommissionBill bill = extractBillData(lines.get(i));
            if(StringUtils.isBlank(bill.getPolicyNo())&&StringUtils.isNotBlank(bill.getPlanName())){

                List<TextPosition> curBillLine = lines.get(i);
                List<TextPosition> hisBillLine = lines.get(i-1);
                 System.out.println("bill.getPlanName()"+billList.get(billList.size()-1).getPolicyNo());

                System.out.println("curBillLine.get(0).getY()"+curBillLine.get(0).getY()+"hisBillLine.get(0).getY()"+hisBillLine.get(0).getY()+"bill.getPlanName()"+bill.getPlanName());
                //如果本行的y坐标和上一行相差小于12，就认为是同一行,修改上一行的计划名称
                if(i>firstDataRowIndex&&Math.abs(lines.get(i).get(0).getY()-lines.get(i-1).get(0).getY())<12) {
                    billList.get(billList.size()-1).setPlanName(billList.get(billList.size()-1).getPlanName()+" "+bill.getPlanName());
                }
            }
            if (bill != null && isValidBill(bill)) {
                billList.add(bill);
            }
        }

        return billList;
    }

    /**
     * Process Life Asia format PDF
     *
     * @return List of commission bills
     */
    public List<CrmCommissionBill> processLifeAsiaFormat() {
        List<CrmCommissionBill> billList = new ArrayList<>();

        // Extract document date
        extractDocumentDate();

        // Find table header
        int headerIndex = findTableHeader("Policy No.", "Commission");
        if (headerIndex == -1) {
            return billList;
        }

        // Find data rows
        int firstDataRowIndex = findFirstDataRow(headerIndex);
        int lastDataRowIndex = findLastDataRow(firstDataRowIndex);

        if (firstDataRowIndex == -1) {
            return billList;
        }

        // Identify columns
//        identifyColumns(headerIndex, firstDataRowIndex, lastDataRowIndex);
        float columnToleranceHead =(float) 4.0 ;//坐标容差值
        float columnToleranceData =(float) 3.0 ;//坐标容差值
        List<ColumnInfo> headerColumns = processMultilineHeader(headerIndex,lastDataRowIndex,firstDataRowIndex, columnToleranceHead,columnToleranceData) ;


        // Extract data rows
        for (int i = firstDataRowIndex; i <= lastDataRowIndex; i++) {
            CrmCommissionBill bill = extractBillData(lines.get(i));
            if (bill != null && isValidBill(bill)) {
                billList.add(bill);
            }
        }

        return billList;
    }

    /**
     * Process generic format PDF
     *
     * @return List of commission bills
     */
    public List<CrmCommissionBill> processGenericFormat() {
        List<CrmCommissionBill> billList = new ArrayList<>();

        // Extract document date
        extractDocumentDate();

        // Find best matching header row
        int headerIndex = findBestHeaderMatch();
        if (headerIndex == -1) {
            return billList;
        }

        // Find data rows
        int firstDataRowIndex = findFirstDataRow(headerIndex);
        int lastDataRowIndex = findLastDataRow(firstDataRowIndex);

        if (firstDataRowIndex == -1) {
            return billList;
        }

        // Identify columns
//        identifyColumns(headerIndex, firstDataRowIndex, lastDataRowIndex);
        float columnToleranceHead =(float) 4.0 ;//坐标容差值
        float columnToleranceData =(float) 3.0 ;//坐标容差值
        List<ColumnInfo> headerColumns = processMultilineHeader(headerIndex,lastDataRowIndex,firstDataRowIndex, columnToleranceHead,columnToleranceData) ;

        // Extract data rows
        for (int i = firstDataRowIndex; i <= lastDataRowIndex; i++) {
            CrmCommissionBill bill = extractBillData(lines.get(i));
            if (bill != null && isValidBill(bill)) {
                billList.add(bill);
            }
        }

        return billList;
    }

    /**
     * Extract document date from the PDF
     */
    private void extractDocumentDate() {
        Pattern datePattern = Pattern.compile("(\\d{2}[/\\-]\\d{2}[/\\-]\\d{4})");

        for (List<TextPosition> line : lines) {
            String lineText = getTextFromPositions(line);

            if (lineText.contains("STATEMENT") || lineText.contains("Date") || lineText.contains("日期")) {
                Matcher dateMatcher = datePattern.matcher(lineText);
                if (dateMatcher.find()) {
                    documentDate = dateMatcher.group(1);
                    break;
                }
            }
        }
    }

    /**
     * Find table header containing specified keywords
     *
     * @param keywords Keywords to look for
     * @return Index of the header row, or -1 if not found
     */
    private int findTableHeader(String... keywords) {
        for (int i = 0; i < lines.size(); i++) {
            String lineText = getTextFromPositions(lines.get(i));

            for (String keyword : keywords) {
                if (lineText.contains(keyword)) {
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * Find the best matching header row based on field mappings
     *
     * @return Index of the best matching header row, or -1 if not found
     */
    private int findBestHeaderMatch() {
        int bestIndex = -1;
        int bestMatchCount = 0;

        for (int i = 0; i < lines.size(); i++) {
            String lineText = getTextFromPositions(lines.get(i));
            int matchCount = 0;

            for (String headerField : fieldMappings.keySet()) {
                if (lineText.contains(headerField)) {
                    matchCount++;
                }
            }

            if (matchCount > bestMatchCount) {
                bestMatchCount = matchCount;
                bestIndex = i;
            }

            // If we found a good match, stop searching
            if (matchCount >= 3) {
                break;
            }
        }

        return bestIndex;
    }

    /**
     * Find the first data row after the header
     *
     * @param headerIndex Index of the header row
     * @return Index of the first data row, or -1 if not found
     */
    private int findFirstDataRow(int headerIndex) {
        for (int i = headerIndex + 1; i < lines.size(); i++) {
            String lineText = getTextFromPositions(lines.get(i));

            // Data rows typically contain numbers and don't contain header keywords
            if (lineText.matches(".*\\d+.*") &&
                !lineText.contains("Policy") &&
                !lineText.contains("Total")) {
                return i;
            }
        }
        return -1;
    }

    /**
     * Find the last data row
     *
     * @param firstDataRowIndex Index of the first data row
     * @return Index of the last data row, or the first data row if no more are found
     */
    private int findLastDataRow(int firstDataRowIndex) {
        int lastIndex = firstDataRowIndex;

        for (int i = firstDataRowIndex + 1; i < lines.size(); i++) {
            String lineText = getTextFromPositions(lines.get(i));

            // Stop at total row or end of data section
            if (lineText.contains("Total") || lineText.contains("总计")) {
                break;
            }

            // Data rows typically contain numbers
            if (lineText.matches(".*\\d+.*") && !lineText.contains("Page")) {
                lastIndex = i;
            }
        }

        return lastIndex;
    }

    /**
     * Identify columns based on header and data rows
     *
     * @param headerIndex Index of the header row
     * @param firstDataRowIndex Index of the first data row
     * @param lastDataRowIndex Index of the last data row
     */
    private void identifyColumns(int headerIndex, int firstDataRowIndex, int lastDataRowIndex) {
        columns.clear();

        // Collect all text positions from header and data rows
        Map<Float, ColumnData> columnDataMap = new HashMap<>();
        float columnTolerance = 4.0f; // X coordinate tolerance

        // Process header row first
        for (TextPosition pos : lines.get(headerIndex)) {
            float x = pos.getX();
            float endX = x + pos.getWidth();

            ColumnData colData = new ColumnData();
            colData.startX = x;
            colData.endX = endX;
            colData.text = new StringBuilder(pos.getUnicode());

            columnDataMap.put(x, colData);
        }

        // Process data rows to refine column boundaries
        for (int i = firstDataRowIndex; i <= lastDataRowIndex; i++) {
            for (TextPosition pos : lines.get(i)) {
                float x = pos.getX();
                float endX = x + pos.getWidth();
                boolean added = false;

                // Skip spaces
                if (pos.getUnicode().equals(" ")) {
                    continue;
                }

                // Try to match with existing columns
                for (Float startX : new ArrayList<>(columnDataMap.keySet())) {
                    ColumnData colData = columnDataMap.get(startX);

                    if (x >= startX - columnTolerance && x <= colData.endX + columnTolerance) {
                        // Update column boundaries
                        colData.startX = Math.min(colData.startX, x);
                        colData.endX = Math.max(colData.endX, endX);
                        added = true;
                        break;
                    }
                }

                // If not matched with any existing column, create a new one
                if (!added) {
                    ColumnData colData = new ColumnData();
                    colData.startX = x;
                    colData.endX = endX;
                    colData.text = new StringBuilder();

                    columnDataMap.put(x, colData);
                }
            }
        }

        // Merge overlapping columns
        columnDataMap = mergeOverlappingIntervals(columnDataMap);

        // Extract header text for each column
        for (TextPosition pos : lines.get(headerIndex)) {
            float x = pos.getX();

            for (Float startX : columnDataMap.keySet()) {
                ColumnData colData = columnDataMap.get(startX);

                if (x >= startX - columnTolerance && x <= colData.endX + columnTolerance) {
                    colData.text.append(pos.getUnicode());
                    break;
                }
            }
        }

        // Create column info objects
        for (Map.Entry<Float, ColumnData> entry : columnDataMap.entrySet()) {
            ColumnData colData = entry.getValue();
            columns.add(new ColumnInfo(colData.text.toString().trim(), colData.startX, colData.endX));
        }

        // Sort columns by X position
        Collections.sort(columns, Comparator.comparing(col -> col.startX));
    }

    private List<ColumnInfo> processMultilineHeader(int tableHeaderIndex, int lastDataRowIndex,int firstDataRowIndex,float columnToleranceHead, float columnToleranceData) {

    	// 这个方法使用数据行来辅助识别列的位置
        List<ColumnInfo> columns = new ArrayList<>();

        if (tableHeaderIndex == -1) return null;

        // 确定表头部分的行范围
        int headerStartIndex = tableHeaderIndex;
        int headerEndIndex = firstDataRowIndex -1;

        // 向上检查是否有更多表头行
//        for (int i = tableHeaderIndex - 1; i >= 0; i--) {
//            String lineText = getTextFromPositions(lines.get(i));
//            // 检查是否包含表头字段的部分文本
//            if (lineText.contains("Policy") || lineText.contains("Risk") || lineText.contains("Commence")) {
//                headerStartIndex = i;
//            } else {
//                break;
//            }
//        }

        // 向下检查是否有更多表头行
//        for (int i = tableHeaderIndex + 1; i < tableHeaderIndex+6; i++) {
//            String lineText = getTextFromPositions(lines.get(i));
//            if ((lineText.contains("Date") || lineText.contains("HKD")||lineText.contains("Rider")) &&
//                !lineText.matches(".*\\d+.*")) { // 不含数字的可能是表头的一部分
//                headerEndIndex = i;
//            } else {
//                break;
//            }
//        }

        // 合并多行表头
        if (headerStartIndex != headerEndIndex) {



            // 创建一个映射，用于存储每列的文本内容
//            Map<Float, StringBuilder> columnTextMap = new HashMap<>();
        	Map<Float, ColumnData> columnDataMap = new HashMap<>();

//        	Map<Float, ColumnData> columnDataLineMap = new HashMap<>();




        	float columnTolerance = columnToleranceHead; // X坐标容差

          //收集所有表头行和数据行的坐标 做交叉合并处理得出 每列最大坐标范围
            for (int i = headerStartIndex; i <= lastDataRowIndex; i++) {//headerEndIndex

        	    Map<Float, ColumnData> columnDataLineMap = new HashMap<>();
        	    if(i > headerEndIndex) {
        	    	columnTolerance = columnToleranceData;
        	    }
            	for (TextPosition pos : lines.get(i)) {
            	    float x = pos.getX();
            	    float endX = x + pos.getWidth();
            	    boolean added = false;
            	    if(pos.getUnicode().equals(" ")) {
//            	    	System.out.println("pos.getUnicode----------------:"+pos.getUnicode());
            	    	continue;
            	    }
            	    for (Float startX : columnDataLineMap.keySet()) {
            	        ColumnData colData = columnDataLineMap.get(startX);

            	        if (x >= startX - columnTolerance && x <= colData.endX + columnTolerance) {

            	            // Update the end X position

            	            colData.endX = Math.max(colData.endX, endX);
            	            colData.startX = Math.min(colData.startX, x);


            	            added = true;
            	            break;
            	        }
            	    }

            	    // If not found in any existing column, create a new one
            	    if (!added) {
            	        ColumnData colData = new ColumnData();
            	        colData.startX = x;
            	        colData.endX = endX;
//            	        colData.text = new StringBuilder(pos.getUnicode());
            	        if(columnDataMap.get(x)!=null) {
            	        	colData.endX = Math.max(colData.endX, columnDataMap.get(x).endX);
            	        }
            	        columnDataLineMap.put(x, colData);
            	    }


            	}

            	columnDataMap.putAll(columnDataLineMap);

            }


            columnDataMap =  mergeOverlappingIntervals(columnDataMap);

          //生成表头文本
            for (int i = headerStartIndex; i <= headerEndIndex; i++) {//headerEndIndex

            	int j=0;

            	 //只爲了加表头换行空格  可删除
        	    for (Float startX : columnDataMap.keySet()) {
        	        ColumnData colData = columnDataMap.get(startX);
        	        if(j==0) {
    	        		if (colData.text.length() > 0) {
        	                colData.text.append(" ");
        	            }
            	    }
        	    }
        	    j++;
            	for (TextPosition pos : lines.get(i)) {
            	    float x = pos.getX();

            	    for (Float startX : columnDataMap.keySet()) {
            	        ColumnData colData = columnDataMap.get(startX);

            	        if (x >= startX - columnTolerance && x <= colData.endX + columnTolerance) {
//            	        	if(!pos.getUnicode().equals("(")&&!pos.getUnicode().equals(")")) {
            	        		colData.text.append(pos.getUnicode());
//            	        	}

            	            break;
            	        }
            	    }



            	}



            }



            // 打印合并后的表头信息
            System.out.println("=== 合并后的表头信息 ===");
            for (Map.Entry<Float, ColumnData> entry : columnDataMap.entrySet()) {
                System.out.println("X坐标key: " + entry.getKey()+"-- 坐标startX: " + entry.getValue().startX  +"-- 坐标endX: " + entry.getValue().endX +", 文本: " + entry.getValue().text);

                columns.add(new ColumnInfo(entry.getValue().text.toString(), (float)entry.getValue().startX, (float)entry.getValue().endX));
            }
        }


        return columns ;
    }


    public static Map<Float, ColumnData> mergeOverlappingIntervals(Map<Float, ColumnData> originalMap) {
        // 如果map为空或只有一个元素，直接返回
        if (originalMap == null || originalMap.size() <= 1) {
            return originalMap;
        }

        // 创建一个新的TreeMap以保证按key(startX)排序
        TreeMap<Float, ColumnData> sortedMap = new TreeMap<>(originalMap);

        // 结果Map
        Map<Float, ColumnData> resultMap = new TreeMap<>();

        // 处理第一个元素
        Iterator<Map.Entry<Float, ColumnData>> iterator = sortedMap.entrySet().iterator();
        Map.Entry<Float, ColumnData> currentEntry = iterator.next();
        float currentKey = currentEntry.getKey();
        float currentStartX = currentEntry.getValue().getStartX();
        float currentEndX = currentEntry.getValue().getEndX();

        // 遍历剩余元素
        while (iterator.hasNext()) {
            Map.Entry<Float, ColumnData> nextEntry = iterator.next();
            ColumnData nextData = nextEntry.getValue();

            // 检查是否有重叠
            if (currentEndX >= nextData.getStartX()) {
                // 有重叠，合并区间
                currentEndX = Math.max(currentEndX, nextData.getEndX());
            } else {
                // 无重叠，将当前区间添加到结果，并更新当前区间
                resultMap.put(currentKey, new ColumnData(currentStartX, currentEndX));
                currentKey = nextEntry.getKey();
                currentStartX = nextData.getStartX();
                currentEndX = nextData.getEndX();
            }
        }

        // 添加最后一个区间
        resultMap.put(currentKey, new ColumnData(currentStartX, currentEndX));

        return resultMap;
    }

    /**
     * Extract bill data from a line of text positions
     *
     * @param line Line of text positions
     * @return Commission bill object
     */
    private CrmCommissionBill extractBillData(List<TextPosition> line) {
        CrmCommissionBill bill = new CrmCommissionBill();
        bill.setCompanyCode(companyCode);

        // Extract values for each column
        for (ColumnInfo column : columns) {
            String value = extractTextInRange(line, column.startX, column.endX).trim();
            
            // Map column to bill field based on column name
            String columnName = column.name.replaceAll("[\\s\\r\\n]", "");//.toLowerCase();
            
            if(StringUtils.isNotBlank(value)) {
            	System.out.println("value----------"+value+"-----"+columnName);
            }

            String fieldName = fieldMappings.get(columnName);
            if(null!=fieldName) {

            	try {
                    switch (fieldName) {
                        case "policyNo":
                            bill.setPolicyNo(value.toString());
                            break;
                        case "inforceDate":
                            bill.setInforceDate(parseDate(value));
                            break;
                        case "transactionDate":
                            bill.setTransactionDate(parseDate(value));
                            break;
                        case "planName":
                            bill.setPlanName(value.toString());
                            break;
                        case "planCode":
                            bill.setPlanCode(value.toString());
                            break;
                        case "paymentCurrency":
                            bill.setPaymentCurrency(value.toString());
                            break;
                        case "premiumAmount":
                            bill.setPremiumAmount(parseDecimal(value));
                            break;
                        case "commissionRate":
                            bill.setCommissionRate(parseDecimal(value));
                            break;
                        case "commissionAmount":
                            bill.setCommissionAmount(parseDecimal(value));
                            break;
                        case "fyp":
                            bill.setFyp(parseDecimal(value));
                            break;
                        case "rp":
                            bill.setRp(parseDecimal(value));
                            break;
                        case "rc":
                            bill.setRc(parseDecimal(value));
                            break;
                        case "afyc":
                            bill.setAfyc(parseDecimal(value));
                            break;
                        case "fyc":
                            bill.setFyc(parseDecimal(value));
                            break;
                        case "rcExchangeRate":
                            bill.setRcExchangeRate(parseDecimal(value));
                            break;
                        // case "saleName":
                        //     bill.setSaleName(value.toString());
                        //     break;
                        default:
                            log.warn("Unknown field: {}", fieldName);
                    }
                } catch (Exception e) {
                    log.warn("Failed to set field value: {} = {}", fieldName, value, e);
                }

            }

        }

        if(bill.getCommissionAmount()==null) {
        	if(bill.getAfyc()!=null&&bill.getAfyc().compareTo(BigDecimal.ZERO)>0) {
        		bill.setCommissionAmount(bill.getAfyc()) ;
            }else if(bill.getFyc()!=null&&bill.getFyc().compareTo(BigDecimal.ZERO)>0){
            	bill.setCommissionAmount(bill.getFyc());
            }else {
            	bill.setCommissionAmount(bill.getRc());
            }
        }

        if(bill.getPremiumAmount()==null) {
        	if(bill.getFyp()!=null&&bill.getFyp().compareTo(BigDecimal.ZERO)>0){
        		bill.setPremiumAmount(bill.getFyp());
            }else {
            	bill.setPremiumAmount(bill.getRp());
            }

        }
        return bill;
    }

    /**
     * Check if a bill is valid
     *
     * @param bill Commission bill
     * @return True if valid, false otherwise
     */
    private boolean isValidBill(CrmCommissionBill bill) {
        return StringUtils.isNotBlank(bill.getPolicyNo()) &&
               (bill.getPremiumAmount() != null || bill.getCommissionAmount() != null);
    }

    /**
     * Extract text within a range of X coordinates
     *
     * @param line Line of text positions
     * @param startX Start X coordinate
     * @param endX End X coordinate
     * @return Extracted text
     */
    private String extractTextInRange(List<TextPosition> line, float startX, float endX) {
        StringBuilder text = new StringBuilder();

        for (TextPosition pos : line) {
            float x = pos.getX();
            float width = pos.getWidth();
            float posEndX = x + width;

            // Check if text position is within range
            if ((x >= startX && x < endX) ||
                (posEndX > startX && posEndX <= endX) ||
                (x <= startX && posEndX >= endX)) {
                text.append(pos.getUnicode());
            }
        }

        return text.toString();
    }

    /**
     * Get text from a list of text positions
     *
     * @param positions List of text positions
     * @return Extracted text
     */
    private String getTextFromPositions(List<TextPosition> positions) {
        StringBuilder text = new StringBuilder();
        for (TextPosition pos : positions) {
            text.append(pos.getUnicode());
        }
        return text.toString();
    }

    /**
     * Parse a date string with improved format detection
     * Supports numeric formats (dd/MM/yyyy) and month name formats (dd-MMM-yyyy)
     *
     * @param dateStr Date string
     * @return Parsed date
     */
    private Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        // Clean up the date string
        String cleanDateStr = dateStr.trim();

        try {
            // First, check for month name format (e.g., 31-May-2021)
            if (containsMonthName(cleanDateStr)) {
                return parseMonthNameDate(cleanDateStr);
            }

            // Next, try to determine the most likely format based on the pattern
            String format = detectDateFormat(cleanDateStr);
            if (format != null) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(format);
                    sdf.setLenient(false); // Strict parsing to avoid invalid dates
                    return sdf.parse(cleanDateStr);
                } catch (ParseException e) {
                    // If detected format fails, continue with other formats
                }
            }

            // Try different date formats in order of likelihood
            String[] formats = {
                "dd/MM/yyyy", // European format (most common for dates like 19/05/2021)
                "MM/dd/yyyy", // US format
                "yyyy/MM/dd", // ISO-like format with slashes
                "dd-MM-yyyy", // European format with dashes
                "MM-dd-yyyy", // US format with dashes
                "yyyy-MM-dd"  // ISO format
            };

            for (String fmt : formats) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(fmt);
                    sdf.setLenient(false); // Strict parsing to avoid invalid dates
                    return sdf.parse(cleanDateStr);
                } catch (ParseException e) {
                    // Try next format
                }
            }

            // If all standard formats fail, try with different separators
            if (cleanDateStr.contains(".")) {
                String[] dotFormats = {"dd.MM.yyyy", "MM.dd.yyyy", "yyyy.MM.dd"};
                for (String fmt : dotFormats) {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat(fmt);
                        sdf.setLenient(false);
                        return sdf.parse(cleanDateStr);
                    } catch (ParseException e) {
                        // Try next format
                    }
                }
            }
        } catch (Exception e) {
            // Log the error for debugging
            System.out.println("Error parsing date '" + cleanDateStr + "': " + e.getMessage());
        }

        return null;
    }

    /**
     * Check if a date string contains a month name (Jan, Feb, Mar, etc.)
     *
     * @param dateStr Date string
     * @return True if the string contains a month name
     */
    private boolean containsMonthName(String dateStr) {
        String[] monthNames = {
            "Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec",
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        };

        String upperDateStr = dateStr.toUpperCase();
        for (String month : monthNames) {
            if (upperDateStr.contains(month.toUpperCase())) {
                return true;
            }
        }

        return false;
    }

    /**
     * Parse a date string that contains a month name
     *
     * @param dateStr Date string with month name
     * @return Parsed date
     */
    private Date parseMonthNameDate(String dateStr) {
        // Try different formats with month names
        String[] formats = {
            "dd-MMM-yyyy",     // 31-May-2021
            "dd MMM yyyy",     // 31 May 2021
            "dd-MMM-yy",       // 31-May-21
            "dd MMM yy",       // 31 May 21
            "MMM dd, yyyy",    // May 31, 2021
            "MMMM dd, yyyy",   // May 31, 2021
            "dd MMMM yyyy",    // 31 May 2021
            "yyyy-MMM-dd",     // 2021-May-31
            "MMM-dd-yyyy"      // May-31-2021
        };

        for (String format : formats) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.ENGLISH);
                sdf.setLenient(false);
                return sdf.parse(dateStr);
            } catch (ParseException e) {
                // Try next format
            }
        }

        return null;
    }

    /**
     * Detect the most likely date format based on the pattern of the date string
     *
     * @param dateStr Date string
     * @return The most likely date format pattern, or null if undetermined
     */
    private String detectDateFormat(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }

        // Check for common separators
        char separator = '/';
        if (dateStr.contains("-")) {
            separator = '-';
        } else if (dateStr.contains(".")) {
            separator = '.';
        } else if (!dateStr.contains("/")) {
            return null; // No recognized separator
        }

        // Split the date string by the separator
        String[] parts = dateStr.split(String.valueOf(separator));
        if (parts.length != 3) {
            return null; // Not a standard date format
        }

        // Check the pattern based on the parts
        if (parts[0].length() == 4) {
            // If first part is 4 digits, likely yyyy-MM-dd
            return "yyyy" + separator + "MM" + separator + "dd";
        } else if (parts[2].length() == 4) {
            // If third part is 4 digits
            // Check if first part is likely day or month
            int firstPart = -1;
            try {
                firstPart = Integer.parseInt(parts[0]);
            } catch (NumberFormatException e) {
                return null;
            }

            if (firstPart > 12) {
                // If first part > 12, it must be a day (dd/MM/yyyy)
                return "dd" + separator + "MM" + separator + "yyyy";
            } else if (firstPart > 0) {
                // If first part is 1-12, it could be either day or month
                // For dates like 01/05/2021, prefer dd/MM/yyyy (European format)
                // as it's more common internationally
                return "dd" + separator + "MM" + separator + "yyyy";
            }
        }

        // Default to dd/MM/yyyy if we can't determine
        return "dd" + separator + "MM" + separator + "yyyy";
    }

    /**
     * Parse a decimal string
     *
     * @param decimalStr Decimal string
     * @return Parsed decimal
     */
    private BigDecimal parseDecimal(String decimalStr) {
        if (StringUtils.isBlank(decimalStr)) {
            return null;
        }

        try {
            // Remove any non-numeric characters except decimal point and minus sign
            String cleanStr = decimalStr.replaceAll("[^0-9\\.\\-]", "");
            return new BigDecimal(cleanStr);
        } catch (Exception e) {
            // Ignore parsing errors
        }

        return null;
    }

    /**
     * Column data class
     */
    private static class ColumnData {
        float startX;
        float endX;
        StringBuilder text;

        public ColumnData() {

        }

        public ColumnData(float startX, float endX) {
            this.startX = startX;
            this.endX = endX;
            this.text = new StringBuilder() ;
        }

        public ColumnData(float startX, float endX,StringBuilder text) {
            this.startX = startX;
            this.endX = endX;
            this.text = text;
        }

        public float getStartX() {
            return startX;
        }

        public void setStartX(float startX) {
            this.startX = startX;
        }

        public float getEndX() {
            return endX;
        }

        public void setEndX(float endX) {
            this.endX = endX;
        }

        public StringBuilder getText() {
            return text;
        }

        public void setEndX(StringBuilder text) {
            this.text = text;
        }


        @Override
        public String toString() {
            return "ColumnData{startX=" + startX + ", endX=" + endX +",text="+text+ '}';
        }
    }

    /**
     * Column information class
     */
    private static class ColumnInfo {
        String name;
        float startX;
        float endX;

        public ColumnInfo(String name, float startX, float endX) {
            this.name = name;
            this.startX = startX;
            this.endX = endX;
        }
    }
}

