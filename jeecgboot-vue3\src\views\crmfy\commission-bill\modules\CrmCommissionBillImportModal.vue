<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="导入保险公司佣金账单" @ok="handleSubmit">
    <div style="padding: 20px;">
      <a-form :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="公司编码" name="companyCode" :rules="[{ required: true, message: '请选择保险公司' }]">
          <a-select
            v-model:value="formData.companyCode"
            placeholder="请选择保险公司"
            style="width: 100%"
            :loading="loadingDict"
          >
            <a-select-option v-for="item in companyOptions" :key="item.value" :value="item.value">
              {{ item.text }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="账单文件" name="file" :rules="[{ required: true, message: '请选择要导入的文件' }]">
          <a-upload
            v-model:file-list="fileList"
            :before-upload="beforeUpload"
            :max-count="1"
            accept=".xlsx,.xls,.pdf"
            @remove="handleRemove"
          >
            <a-button>
              <upload-outlined />
              选择文件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts">
  import { ref, reactive, defineComponent, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { importCompanyBill } from '../CrmCommissionBill.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import { initDictOptions } from '/@/utils/dict';

  export default defineComponent({
    name: 'CrmCommissionBillImportModal',
    components: {
      BasicModal,
      UploadOutlined,
    },
    emits: ['success', 'register'],
    setup(props, { emit }) {

      const { createMessage } = useMessage();

      // 表单数据
      const formData = reactive({
        companyCode: '',
        file: null,
      });

      // 文件列表
      const fileList = ref([]);

      // 字典数据
      const companyOptions = ref([]);
      const loadingDict = ref(false);

      // 加载字典数据
      async function loadCompanyDict() {
        try {
          loadingDict.value = true;
          const result = await initDictOptions('supplier');
          if (result && Array.isArray(result)) {
            companyOptions.value = result;
          }
        } catch (error) {
          console.error('加载保险公司字典失败:', error);
        } finally {
          loadingDict.value = false;
        }
      }

      // 组件挂载时加载字典
      onMounted(() => {
        loadCompanyDict();
      });

      //表单赋值
      const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
        //重置表单
        formData.companyCode = '';
        formData.file = null;
        fileList.value = [];
        setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
      });

      // 上传前处理
      function beforeUpload(file) {
        formData.file = file;
        return false; // 阻止自动上传
      }

      // 移除文件
      function handleRemove() {
        formData.file = null;
      }

      //表单提交
      async function handleSubmit() {
        try {
          setModalProps({ confirmLoading: true });

          if (!formData.file) {
            createMessage.error('请选择要导入的文件');
            return;
          }

          if (!formData.companyCode) {
            createMessage.error('请选择保险公司');
            return;
          }

          //提交表单
          const result = await importCompanyBill({
            file: formData.file,
            companyCode: formData.companyCode,
          });

          if (result.success) {
            createMessage.success(result.message || '导入成功');
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
          } else {
            createMessage.error(result.message || '导入失败');
          }
        } 
        
        catch (error) {
          createMessage.error('导入失败: ' + error.message);
        } finally {
          setModalProps({ confirmLoading: false });
        }
      }

      return {
        formData,
        fileList,
        companyOptions,
        loadingDict,
        registerModal,
        beforeUpload,
        handleRemove,
        handleSubmit,
      };
    },
  });
</script>
