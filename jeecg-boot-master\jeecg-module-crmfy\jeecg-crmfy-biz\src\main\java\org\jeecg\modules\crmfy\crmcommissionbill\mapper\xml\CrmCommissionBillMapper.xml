<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.crmfy.crmcommissionbill.mapper.CrmCommissionBillMapper">

    <!-- 分页查询佣金账单对账信息 -->
    <select id="queryBillCompare" resultType="org.jeecg.modules.crmfy.crmcommissionbill.vo.CrmCommissionBillVO">
        SELECT
            COALESCE(calc.policy_no, bill.policy_no) as policyNo,
            COALESCE(calc.plan_name, bill.plan_name) as planName,
            COALESCE(calc.year_num, bill.year_num) as yearNum,
            COALESCE(calc.payment_currency, bill.payment_currency) as paymentCurrency,

            -- 公司台账信息
            calc.premium_amount as ledgerPremiumAmount,
            calc.commission_amount as ledgerCommissionAmount,
            calc.calculation_date as ledgerCalculationDate,

            -- 保险公司账单信息
            bill.company_code as companyCode,
            bill.inforce_date as inforceDate,
            bill.premium_amount as billPremiumAmount,
            bill.commission_amount as billCommissionAmount,
            bill.update_time as importTime,

            -- 对账状态和差额计算
            CASE
                WHEN calc.policy_no IS NULL THEN 'MISSING_LEDGER'
                WHEN bill.policy_no IS NULL THEN 'MISSING_BILL'
                WHEN ABS(COALESCE(calc.commission_amount, 0) - COALESCE(bill.commission_amount, 0)) &lt; 0.01 THEN 'MATCH'
                ELSE 'MISMATCH'
            END as reconcileStatus,

            (COALESCE(calc.commission_amount, 0) - COALESCE(bill.commission_amount, 0)) as commissionDifference

        FROM (
            -- 公司台账数据
            SELECT
                policy_no, plan_name, year_num, payment_currency,
                premium_amount, commission_amount, calculation_date
            FROM crm_commission_calculation
            WHERE del_flag = 0 and payout_date &lt;= SYSDATE()
            <if test="po.policyNo != null and po.policyNo != ''">
                AND policy_no LIKE CONCAT('%', #{po.policyNo}, '%')
            </if>
            <if test="po.yearNum != null">
                AND year_num = #{po.yearNum}
            </if>
            <if test="po.planName != null and po.planName != ''">
                AND plan_name LIKE CONCAT('%', #{po.planName}, '%')
            </if>
            <if test="po.companyCode != null and po.companyCode != ''">
                AND company_code = #{po.companyCode}
            </if>
        ) calc

        LEFT JOIN (
            -- 保险公司账单数据
            SELECT
                policy_no, plan_name, year_num, payment_currency, company_code,
                inforce_date, premium_amount, commission_amount, update_time
            FROM crm_commission_bill
            WHERE del_flag = 0
            <if test="po.policyNo != null and po.policyNo != ''">
                AND policy_no LIKE CONCAT('%', #{po.policyNo}, '%')
            </if>
            <if test="po.yearNum != null">
                AND year_num = #{po.yearNum}
            </if>
            <if test="po.planName != null and po.planName != ''">
                AND plan_name LIKE CONCAT('%', #{po.planName}, '%')
            </if>
            <if test="po.companyCode != null and po.companyCode != ''">
                AND company_code = #{po.companyCode}
            </if>
        ) bill

        ON calc.policy_no = bill.policy_no
        AND calc.year_num = bill.year_num
        AND calc.plan_name = bill.plan_name

        UNION

        SELECT
            COALESCE(calc.policy_no, bill.policy_no) as policyNo,
            COALESCE(calc.plan_name, bill.plan_name) as planName,
            COALESCE(calc.year_num, bill.year_num) as yearNum,
            COALESCE(calc.payment_currency, bill.payment_currency) as paymentCurrency,

            -- 公司台账信息
            calc.premium_amount as ledgerPremiumAmount,
            calc.commission_amount as ledgerCommissionAmount,
            calc.calculation_date as ledgerCalculationDate,

            -- 保险公司账单信息
            bill.company_code as companyCode,
            bill.inforce_date as inforceDate,
            bill.premium_amount as billPremiumAmount,
            bill.commission_amount as billCommissionAmount,
            bill.update_time as importTime,

            -- 对账状态和差额计算
            CASE
                WHEN calc.policy_no IS NULL THEN 'MISSING_LEDGER'
                WHEN bill.policy_no IS NULL THEN 'MISSING_BILL'
                WHEN ABS(COALESCE(calc.commission_amount, 0) - COALESCE(bill.commission_amount, 0)) &lt; 0.01 THEN 'MATCH'
                ELSE 'MISMATCH'
            END as reconcileStatus,

            (COALESCE(calc.commission_amount, 0) - COALESCE(bill.commission_amount, 0)) as commissionDifference

        FROM (
            -- 保险公司账单数据
            SELECT
                policy_no, plan_name, year_num, payment_currency, company_code,
                inforce_date, premium_amount, commission_amount, update_time
            FROM crm_commission_bill
            WHERE del_flag = 0
            <if test="po.policyNo != null and po.policyNo != ''">
                AND policy_no LIKE CONCAT('%', #{po.policyNo}, '%')
            </if>
            <if test="po.yearNum != null">
                AND year_num = #{po.yearNum}
            </if>
            <if test="po.planName != null and po.planName != ''">
                AND plan_name LIKE CONCAT('%', #{po.planName}, '%')
            </if>
            <if test="po.companyCode != null and po.companyCode != ''">
                AND company_code = #{po.companyCode}
            </if>
        ) bill

        LEFT JOIN (
            -- 公司台账数据
            SELECT
                policy_no, plan_name, year_num, payment_currency,
                premium_amount, commission_amount, calculation_date
            FROM crm_commission_calculation
            WHERE del_flag = 0 and payout_date &lt;= SYSDATE()
            <if test="po.policyNo != null and po.policyNo != ''">
                AND policy_no LIKE CONCAT('%', #{po.policyNo}, '%')
            </if>
            <if test="po.yearNum != null">
                AND year_num = #{po.yearNum}
            </if>
            <if test="po.planName != null and po.planName != ''">
                AND plan_name LIKE CONCAT('%', #{po.planName}, '%')
            </if>
            <if test="po.companyCode != null and po.companyCode != ''">
                AND company_code = #{po.companyCode}
            </if>
        ) calc

        ON bill.policy_no = calc.policy_no
        AND bill.year_num = calc.year_num
        AND bill.plan_name = calc.plan_name

        WHERE calc.policy_no IS NULL

        ORDER BY
            policyNo, yearNum
    </select>

</mapper>